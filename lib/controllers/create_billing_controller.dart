import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:platix/api/api_service.dart';
import 'package:platix/data/models/patient_registration_model.dart';
import 'package:platix/data/models/billing_model.dart';
import 'package:platix/view/screens/dentist/create_billing_screen.dart';

class CreateBillingController extends GetxController {
  final ApiService _apiService = ApiService();
  final BillingScreenMode mode;
  final BillingData? initialBillingData;
  final String? initialPatientName;
  final String? initialServiceName;
  final String? initialAmount;

  CreateBillingController({
    this.mode = BillingScreenMode.create,
    this.initialBillingData,
    this.initialPatientName,
    this.initialServiceName,
    this.initialAmount,
  });

  // Observables for patient search
  var searchPatientController = TextEditingController();
  var searchQuery = ''.obs;
  var searchResults = <PatientData>[].obs;
  var isSearching = false.obs;
  var selectedPatient = Rxn<PatientData>();

  // Loading state for billing data
  var isLoadingBillingData = false.obs;

  // Observables for service search
  var serviceSearchQuery = ''.obs;
  var serviceSearchResults = <ServiceModel>[].obs;
  var isServiceSearching = false.obs;
  var selectedServices = <BillingService>[].obs;
  var allServices = <ServiceModel>[].obs;
  var selectedPaymentSource = RxnString();

  // Form field controllers
  late TextEditingController firstNameController;
  late TextEditingController lastNameController;
  late TextEditingController patientIdController;
  late TextEditingController ageController;
  late TextEditingController dobController;
  late TextEditingController searchServiceController;
  late TextEditingController priceController;
  late TextEditingController discountController;
  late TextEditingController discountAmountController;
  late TextEditingController totalAmountController;
  late TextEditingController paidAmountController;
  late TextEditingController balanceAmountController;

  @override
  void onInit() {
    print('=== CONTROLLER INIT START ===');
    print('Controller instance: ${hashCode}');
    print('Mode: $mode');
    print('Initial billing data: ${initialBillingData?.id}');

    super.onInit();

    print('Initializing text controllers...');
    // Initialize controllers
    firstNameController = TextEditingController();
    lastNameController = TextEditingController();
    patientIdController = TextEditingController();
    ageController = TextEditingController();
    dobController = TextEditingController();
    searchServiceController = TextEditingController();
    priceController = TextEditingController();
    discountController = TextEditingController();
    discountAmountController = TextEditingController();
    totalAmountController = TextEditingController();
    paidAmountController = TextEditingController();
    balanceAmountController = TextEditingController();

    print('Text controllers initialized. Checking mode and initial data...');

    if ((mode == BillingScreenMode.edit || mode == BillingScreenMode.view) && initialBillingData != null) {
      print('Edit/View mode with billing data - calling fetchBillingDetails');
      fetchBillingDetails(initialBillingData!.id!);
    } else if (initialBillingData != null) {
      print('Create mode with initial data - calling populateInitialData');
      populateInitialData(initialBillingData!);
    } else {
      print('Create mode without initial data');
    }

    // Handle initial parameters from screen
    print('Handling initial parameters...');
    _handleInitialParameters();

    // Debounce search queries
    debounce(searchQuery, (query) {
      if (query.length > 1) { // Changed from 2 to 1 for 2+ characters
        searchPatients(query);
      } else {
        searchResults.clear();
      }
    }, time: const Duration(milliseconds: 500));

    // Debounce service search queries
    debounce(serviceSearchQuery, (query) {
      if (query.length > 1) { // 2+ characters for service search
        searchServices(query);
      } else {
        serviceSearchResults.clear();
      }
    }, time: const Duration(milliseconds: 300));

    // Load all services on init
    loadAllServices();

    // Add listeners for automatic calculation
    discountController.addListener(() {
      if (discountController.text.isNotEmpty) {
        discountAmountController.clear();
      }
      calculateTotal();
    });

    discountAmountController.addListener(() {
      if (discountAmountController.text.isNotEmpty) {
        discountController.clear();
      }
      calculateTotal();
    });

    paidAmountController.addListener(updateBalance);
  }

  void _handleInitialParameters() {
    print('=== HANDLE INITIAL PARAMETERS START ===');
    print('Mode: $mode');
    print('Initial patient name: $initialPatientName');
    print('Initial service name: $initialServiceName');
    print('Initial amount: $initialAmount');

    // Only set initial values for create mode or when no billing data exists
    // In edit/view modes with existing billing data, don't override the search fields
    if (mode == BillingScreenMode.create && initialBillingData == null) {
      if (initialPatientName != null) {
        print('Create mode: Setting search patient controller to: $initialPatientName');
        searchPatientController.text = initialPatientName!;
      }
      if (initialServiceName != null) {
        print('Create mode: Setting search service controller to: $initialServiceName');
        searchServiceController.text = initialServiceName!;
      }
      if (initialAmount != null) {
        final cleanAmount = initialAmount!.replaceAll('₹', '').replaceAll(',', '');
        print('Create mode: Setting amount controllers to: $cleanAmount');
        totalAmountController.text = cleanAmount;
        paidAmountController.text = cleanAmount;
      }
    } else {
      print('Edit/View mode with billing data: Skipping initial parameter setting');
    }

    print('=== HANDLE INITIAL PARAMETERS END ===');
  }

  @override
  void onClose() {
    // Dispose all controllers
    searchPatientController.dispose();
    firstNameController.dispose();
    lastNameController.dispose();
    patientIdController.dispose();
    ageController.dispose();
    dobController.dispose();
    searchServiceController.dispose();
    priceController.dispose();
    discountController.dispose();
    discountAmountController.dispose();
    totalAmountController.dispose();
    paidAmountController.dispose();
    balanceAmountController.dispose();
    super.onClose();
  }

  Future<void> searchPatients(String query) async {
    try {
      isSearching.value = true;
      final response = await _apiService.getAllRegistrations(search: query);
      searchResults.value = response.data;
    } catch (e) {
      Get.snackbar('Error', 'Failed to search for patients: $e');
    } finally {
      isSearching.value = false;
    }
  }

  void selectPatient(PatientData patient) {
    selectedPatient.value = patient;
    searchPatientController.text = patient.displayName;

    // Populate form fields
    firstNameController.text = patient.firstName ?? '';
    lastNameController.text = patient.lastName ?? '';
    patientIdController.text = patient.patientRegId ?? '';
    ageController.text = patient.age?.toString() ?? '';
    dobController.text = patient.dob ?? '';

    searchResults.clear();
  }

  Future<void> loadAllServices() async {
    try {
      final response = await _apiService.getAllServices();
      allServices.value = response.data;
    } catch (e) {
      Get.snackbar('Error', 'Failed to load services: $e');
      allServices.value = []; // Initialize with empty list on error
    }
  }

  void searchServices(String query) {
    if (allServices.isEmpty) {
      loadAllServices();
      return;
    }

    final filteredServices = allServices.where((service) {
      final serviceName = service.serviceName?.toLowerCase() ?? '';
      final searchQuery = query.toLowerCase();
      return serviceName.contains(searchQuery);
    }).toList();

    serviceSearchResults.value = filteredServices;
  }

  void selectService(ServiceModel service) {
    // Check if the service is already selected
    final existingServiceIndex = selectedServices.indexWhere((s) => s.serviceId == service.id);

    if (existingServiceIndex != -1) {
      // If service already exists, increment its quantity
      incrementQuantity(selectedServices[existingServiceIndex]);
    } else {
      // Otherwise, add the new service with quantity 1
      selectedServices.add(BillingService(
        serviceId: service.id,
        serviceName: service.serviceName,
        price: service.price,
        quantity: 1,
      ));
    }

    calculateTotal();
    searchServiceController.clear();
    serviceSearchResults.clear();
  }

  void incrementQuantity(BillingService service) {
    final index = selectedServices.indexOf(service);
    if (index != -1) {
      var updatedService = BillingService(
        serviceId: service.serviceId,
        serviceName: service.serviceName,
        price: service.price,
        quantity: service.quantity + 1,
      );
      selectedServices[index] = updatedService;
      calculateTotal();
    }
  }

  void decrementQuantity(BillingService service) {
    final index = selectedServices.indexOf(service);
    if (index != -1 && service.quantity > 1) {
      var updatedService = BillingService(
        serviceId: service.serviceId,
        serviceName: service.serviceName,
        price: service.price,
        quantity: service.quantity - 1,
      );
      selectedServices[index] = updatedService;
      calculateTotal();
    }
  }

  void removeService(BillingService service) {
    selectedServices.remove(service);
    calculateTotal();
  }

  void calculateTotal() {
    double total = 0;
    for (var service in selectedServices) {
      total += service.price * service.quantity;
    }

    // Apply discount
    final discountPercent = double.tryParse(discountController.text) ?? 0;
    final discountAmount = double.tryParse(discountAmountController.text) ?? 0;

    if (discountPercent > 0) {
      total -= total * (discountPercent / 100);
    } else if (discountAmount > 0) {
      total -= discountAmount;
    }

    totalAmountController.text = total.toStringAsFixed(2);
    updateBalance();
  }

  void updateBalance() {
    final total = double.tryParse(totalAmountController.text) ?? 0;
    final paid = double.tryParse(paidAmountController.text) ?? 0;
    final balance = total - paid;
    balanceAmountController.text = balance.toStringAsFixed(2);
  }

  Future<void> fetchBillingDetails(String billingId) async {
    print('=== FETCH BILLING DETAILS START ===');
    print('Controller instance: ${this.hashCode}');
    print('Billing ID: $billingId');
    print('Mode: $mode');

    isLoadingBillingData.value = true;

    try {
      print('Making API call to getBillingById...');
      final response = await _apiService.getBillingById(billingId);

      print('API Response received:');
      print('  Status: ${response.status}');
      print('  Message: ${response.message}');
      print('  Data is null: ${response.data == null}');

      if (response.data != null) {
        print('  Data details:');
        print('    ID: ${response.data!.id}');
        print('    First Name: ${response.data!.firstName}');
        print('    Last Name: ${response.data!.lastName}');
        print('    Patient Reg ID: ${response.data!.patientRegId}');
        print('    Total Amount: ${response.data!.totalAmount}');
        print('    Services count: ${response.data!.services.length}');
      }

      if (response.status && response.data != null) {
        print('Calling populateInitialData...');
        populateInitialData(response.data!);
        print('populateInitialData completed');
      } else {
        print('API response failed or no data');
        Get.snackbar('Error', response.message ?? 'Failed to fetch billing details.');
      }
    } catch (e) {
      print('Exception in fetchBillingDetails: $e');
      print('Exception type: ${e.runtimeType}');
      Get.snackbar('Error', 'An error occurred: $e');
    } finally {
      isLoadingBillingData.value = false;
      print('=== FETCH BILLING DETAILS END ===');
    }
  }

  // Method to return filtered patients for CustomSearchField
  Future<List<PatientData>> getFilteredPatients(String query) async {
    if (query.length <= 1) return [];

    try {
      isSearching.value = true;
      final response = await _apiService.getAllRegistrations(search: query);
      return response.data;
    } catch (e) {
      Get.snackbar('Error', 'Failed to search for patients: $e');
      return [];
    } finally {
      isSearching.value = false;
    }
  }

  // Method to return filtered services for CustomSearchField
  Future<List<ServiceModel>> getFilteredServices(String query) async {
    if (query.length <= 1) return [];

    if (allServices.isEmpty) {
      await loadAllServices();
    }

    final filteredServices = allServices.where((service) {
      final serviceName = service.serviceName?.toLowerCase() ?? '';
      final searchQuery = query.toLowerCase();
      return serviceName.contains(searchQuery);
    }).toList();

    return filteredServices;
  }

  void populateInitialData(BillingData data) {
    print('=== POPULATE INITIAL DATA START ===');
    print('Controller instance: ${hashCode}');
    print('Input data:');
    print('  First Name: ${data.firstName}');
    print('  Last Name: ${data.lastName}');
    print('  Patient Reg ID: ${data.patientRegId}');
    print('  Age: ${data.age}');
    print('  DOB: ${data.dob}');
    print('  Gender: ${data.gender}');
    print('  Mobile: ${data.mobile}');
    print('  Services count: ${data.services.length}');
    print('  Total Amount: ${data.totalAmount}');
    print('  Paid Amount: ${data.paidAmount}');
    print('  Balance Amount: ${data.balanceAmount}');
    print('  Payment Source: ${data.paymentSource}');
    print('  Discount Percent: ${data.discountPercent}');
    print('  Discount Amount: ${data.discountAmount}');

    print('Setting form field controllers...');

    // Populate form field controllers
    firstNameController.text = data.firstName ?? '';
    lastNameController.text = data.lastName ?? '';
    patientIdController.text = data.patientRegId ?? '';
    ageController.text = data.age?.toString() ?? '';
    dobController.text = data.dob ?? '';

    print('Basic fields set. Setting search patient controller...');

    // Populate search patient controller with display name only in view mode
    // In edit mode, keep search fields empty for easy searching
    if (mode == BillingScreenMode.view) {
      searchPatientController.text = data.displayName;
      print('View mode: Set search patient controller to: ${data.displayName}');
    } else {
      searchPatientController.clear();
      print('Edit mode: Cleared search patient controller for new searches');
    }

    print('Setting selected patient...');

    // Set selected patient data
    selectedPatient.value = PatientData(
      firstName: data.firstName,
      lastName: data.lastName,
      patientRegId: data.patientRegId,
      age: data.age?.toString(),
      gender: data.gender,
      mobile: data.mobile,
      dob: data.dob,
    );

    print('Setting services and financial data...');

    // Populate services and financial data
    selectedServices.value = data.services;
    discountController.text = data.discountPercent ?? '';
    discountAmountController.text = data.discountAmount ?? '';
    totalAmountController.text = data.totalAmount ?? '';
    paidAmountController.text = data.paidAmount ?? '';
    balanceAmountController.text = data.balanceAmount ?? '';
    selectedPaymentSource.value = data.paymentSource;

    // Clear service search controller in edit mode for easy searching
    if (mode == BillingScreenMode.edit) {
      searchServiceController.clear();
      print('Edit mode: Cleared service search controller for new searches');
    }

    print('Verifying controller values after setting:');
    print('  firstNameController.text: "${firstNameController.text}"');
    print('  lastNameController.text: "${lastNameController.text}"');
    print('  patientIdController.text: "${patientIdController.text}"');
    print('  ageController.text: "${ageController.text}"');
    print('  dobController.text: "${dobController.text}"');
    print('  searchPatientController.text: "${searchPatientController.text}"');
    print('  totalAmountController.text: "${totalAmountController.text}"');
    print('  paidAmountController.text: "${paidAmountController.text}"');
    print('  balanceAmountController.text: "${balanceAmountController.text}"');
    print('  selectedPaymentSource.value: "${selectedPaymentSource.value}"');
    print('  selectedServices.length: ${selectedServices.length}');
    print('  selectedPatient.value: ${selectedPatient.value?.firstName} ${selectedPatient.value?.lastName}');

    print('Calling update() to refresh UI...');
    // Force UI update
    update();

    print('=== POPULATE INITIAL DATA END ===');
  }

  Future<void> saveBilling() async {
    if (selectedPatient.value == null) {
      Get.snackbar('Error', 'Please select a patient.');
      return;
    }
    if (selectedServices.isEmpty) {
      Get.snackbar('Error', 'Please select at least one service.');
      return;
    }
    if (paidAmountController.text.isEmpty) {
      Get.snackbar('Error', 'Please enter the paid amount.');
      return;
    }
    if (selectedPaymentSource.value == null) {
      Get.snackbar('Error', 'Please select a payment source.');
      return;
    }

    final request = BillingUpsertRequest(
      id: mode == BillingScreenMode.edit ? initialBillingData!.id : null,
      firstName: firstNameController.text,
      lastName: lastNameController.text,
      patientRegId: patientIdController.text,
      gender: selectedPatient.value!.gender!,
      age: int.parse(ageController.text),
      services: selectedServices.map((service) => BillingServiceRequest(
        serviceId: service.serviceId!,
        serviceName: service.serviceName!,
        quantity: service.quantity,
        price: service.price,
      )).toList(),
      discountPercent: discountController.text,
      discountAmount: discountAmountController.text,
      totalAmount: totalAmountController.text,
      paidAmount: paidAmountController.text,
      balanceAmount: balanceAmountController.text,
      paymentSource: selectedPaymentSource.value!,
      mobile: selectedPatient.value!.mobile!,
    );

    try {
      final response = await _apiService.upsertBilling(request);
      if (response.status) {
        Get.snackbar('Success', 'Billing information saved successfully.');
        Get.back(); // Go back to the previous screen
      } else {
        Get.snackbar('Error', response.message ?? 'Failed to save billing information.');
      }
    } catch (e) {
      Get.snackbar('Error', 'An error occurred: $e');
    }
  }
}
