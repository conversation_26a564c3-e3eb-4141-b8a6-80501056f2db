class BillingReportsModel {
  final List<BillingReportData> data;
  final PaginationData pagination;

  BillingReportsModel({
    required this.data,
    required this.pagination,
  });

  factory BillingReportsModel.fromJson(Map<String, dynamic> json) {
    return BillingReportsModel(
      data: (json['data'] as List<dynamic>?)
              ?.map((item) => BillingReportData.fromJson(item))
              .toList() ??
          [],
      pagination: PaginationData.fromJson(json['pagination'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'data': data.map((item) => item.toJson()).toList(),
      'pagination': pagination.toJson(),
    };
  }
}

class BillingReportData {
  final String? patientRegId;
  final String? name;
  final String? mobile;
  final String? paymentSource;
  final String? id;
  final List<BillingReportService> services;
  final String? servicesString; // For when services come as a string

  BillingReportData({
    this.patientRegId,
    this.name,
    this.mobile,
    this.paymentSource,
    this.id,
    this.services = const [],
    this.servicesString,
  });

  factory BillingReportData.fromJson(Map<String, dynamic> json) {
    print('BillingReportData.fromJson - services field type: ${json['services'].runtimeType}');
    print('BillingReportData.fromJson - services value: ${json['services']}');

    // Handle services field - it might be a List, String, or null
    List<BillingReportService> servicesList = [];
    String? servicesStringValue;

    if (json['services'] != null) {
      if (json['services'] is List) {
        print('Services is a List, parsing...');
        // If it's a list, parse each item
        servicesList = (json['services'] as List<dynamic>)
            .map((item) => BillingReportService.fromJson(item))
            .toList();
      } else if (json['services'] is String) {
        print('Services is a String: ${json['services']}');
        // If it's a string, store it for display
        servicesStringValue = json['services'].toString();
        servicesList = [];
      } else {
        print('Services is unexpected type: ${json['services'].runtimeType}');
      }
    } else {
      print('Services field is null');
    }

    return BillingReportData(
      patientRegId: json['patient_reg_id']?.toString(),
      name: json['name']?.toString(),
      mobile: json['mobile']?.toString(),
      paymentSource: json['payment_source']?.toString(),
      id: json['id']?.toString(),
      services: servicesList,
      servicesString: servicesStringValue,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'patient_reg_id': patientRegId,
      'name': name,
      'mobile': mobile,
      'payment_source': paymentSource,
      'id': id,
      'services': servicesString ?? services.map((service) => service.toJson()).toList(),
    };
  }

  // Helper method to get display name with fallback
  String get displayName => name?.isNotEmpty == true ? name! : 'N/A';
  
  // Helper method to get display mobile with fallback
  String get displayMobile => mobile?.isNotEmpty == true ? mobile! : 'N/A';
  
  // Helper method to get display payment source with fallback
  String get displayPaymentSource => paymentSource?.isNotEmpty == true ? paymentSource! : 'N/A';
  
  // Helper method to get display patient reg ID with fallback
  String get displayPatientRegId => patientRegId?.isNotEmpty == true ? patientRegId! : 'N/A';

  // Helper method to get display services
  String get displayServices {
    // If we have a services string (from API), parse and format it
    if (servicesString != null && servicesString!.isNotEmpty) {
      return _formatServicesString(servicesString!);
    }

    // Otherwise, use the parsed services list
    if (services.isEmpty) return 'Services available';
    if (services.length == 1) return services.first.displayServiceName;
    if (services.length == 2) return '${services.first.displayServiceName}, ${services[1].displayServiceName}';
    return '${services.first.displayServiceName} +${services.length - 1} more';
  }

  // Helper method to format services string with smart truncation
  String _formatServicesString(String servicesStr) {
    // Split by common delimiters (comma, semicolon, pipe)
    List<String> serviceNames = servicesStr
        .split(RegExp(r'[,;|]'))
        .map((s) => s.trim())
        .where((s) => s.isNotEmpty)
        .toList();

    if (serviceNames.isEmpty) return 'Services available';
    if (serviceNames.length == 1) return serviceNames.first;
    if (serviceNames.length == 2) return '${serviceNames.first}, ${serviceNames[1]}';

    // For 3+ services, show first service + count
    return '${serviceNames.first} +${serviceNames.length - 1} more';
  }
}

class BillingReportService {
  final String? serviceId;
  final String? serviceName;
  final int quantity;
  final double price;

  BillingReportService({
    this.serviceId,
    this.serviceName,
    this.quantity = 1,
    this.price = 0.0,
  });

  factory BillingReportService.fromJson(Map<String, dynamic> json) {
    return BillingReportService(
      serviceId: json['service_id']?.toString(),
      serviceName: json['servicename']?.toString() ?? json['service_name']?.toString(),
      quantity: json['quantity'] is int ? json['quantity'] : int.tryParse(json['quantity']?.toString() ?? '1') ?? 1,
      price: json['price'] is double
          ? json['price']
          : double.tryParse(json['price']?.toString() ?? '0') ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'service_id': serviceId,
      'service_name': serviceName,
      'quantity': quantity,
      'price': price,
    };
  }

  // Helper methods
  double get totalPrice => price * quantity;
  String get displayServiceName => serviceName ?? 'Unknown Service';
}

class PaginationData {
  final int total;
  final int page;
  final int limit;
  final int totalPages;

  PaginationData({
    required this.total,
    required this.page,
    required this.limit,
    required this.totalPages,
  });

  factory PaginationData.fromJson(Map<String, dynamic> json) {
    return PaginationData(
      total: json['total'] ?? 0,
      page: json['page'] ?? 1,
      limit: json['limit'] ?? 10,
      totalPages: json['totalPages'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total': total,
      'page': page,
      'limit': limit,
      'totalPages': totalPages,
    };
  }

  // Helper methods for pagination
  bool get hasNextPage => page < totalPages;
  bool get hasPreviousPage => page > 1;
  int get nextPage => hasNextPage ? page + 1 : page;
  int get previousPage => hasPreviousPage ? page - 1 : page;
}
