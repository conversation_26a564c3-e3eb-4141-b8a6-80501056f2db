import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:platix/controllers/billing_controller.dart';
import 'package:platix/services/permission_service.dart';
import 'package:platix/utils/app_export.dart';
import 'package:platix/view/screens/dentist/create_billing_screen.dart';
import 'package:platix/data/models/billing_reports_model.dart';
import 'package:platix/data/models/billing_model.dart';
import 'package:platix/view/widgets/billing_report_card.dart';

class BillingScreen extends StatefulWidget {
  const BillingScreen({super.key});

  @override
  State<BillingScreen> createState() => _BillingScreenState();
}

class _BillingScreenState extends State<BillingScreen> {
  final BillingController billingController = Get.put(BillingController());
  final PermissionService permissionService = PermissionService();
  final TextEditingController searchController = TextEditingController();

  void _navigateToViewBilling(BillingReportData billingData) {
    Get.to(() => CreateBillingScreen(
      mode: BillingScreenMode.view,
      showSearchOption: false,
      patientName: billingData.displayName,
      serviceName: billingData.displayPaymentSource,
      amount: 'N/A', // Amount not available in billing reports
      date: 'N/A', // Date not available in billing reports
    ));
  }

  @override
  void dispose() {
    searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: permissionService.hasAnyPermission('billing', ['is_view', 'is_list', 'is_add']),
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Billing Reports'),
        ),
        body: Column(
          children: [
            // Search Section
            Visibility(
              visible: permissionService.hasPermission('billing', 'is_list'),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Billing Reports',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 16),
                    // Search Bar
                    Container(
                      decoration: BoxDecoration(
                        boxShadow: AppDecoration.shadow1_3,
                        borderRadius: BorderRadiusStyle.radius8,
                      ),
                      child: CustomTextFormField(
                        controller: searchController,
                        hintText:
                            'Search by patient name, mobile, or payment source...',
                        prefix: Padding(
                          padding: const EdgeInsets.all(AppSizes.sm),
                          child: CustomImageView(
                            imagePath: AppIcons.search,
                            color: AppColors.primary,
                          ),
                        ),
                        onChanged: (value) {
                          billingController.searchBillingReports(value);
                        },
                      ),
                    ),
                    const SizedBox(height: 16),
                    // Stats Row
                    Obx(() => Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          billingController.totalItemsText,
                          style: const TextStyle(fontSize: 14, color: Colors.grey),
                        ),
                        Text(
                          billingController.currentPageText,
                          style: const TextStyle(fontSize: 14, color: Colors.grey),
                        ),
                      ],
                    )),
                  ],
                ),
              ),
            ),
            // Data Table Section
            Expanded(
              child: Visibility(
                visible: permissionService.hasPermission('billing', 'is_list'),
                child: Obx(() {
                  if (billingController.isLoading.value && !billingController.hasData) {
                    return const Center(child: CircularProgressIndicator());
                  }

                  if (billingController.hasError.value) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(Icons.error, size: 64, color: Colors.red),
                          const SizedBox(height: 16),
                          Text(
                            'Error: ${billingController.errorMessage.value}',
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: () => billingController.refreshBillingReports(),
                            child: const Text('Retry'),
                          ),
                        ],
                      ),
                    );
                  }

                  if (!billingController.hasData) {
                    return const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.receipt_long, size: 64, color: Colors.grey),
                          SizedBox(height: 16),
                          Text('No billing reports found'),
                        ],
                      ),
                    );
                  }

                  return RefreshIndicator(
                    onRefresh: () => billingController.refreshBillingReports(),
                    child: ListView.builder(
                      controller: billingController.scrollController,
                      itemCount: billingController.billingReports.length +
                          (billingController.isLoading.value &&
                                  billingController.hasData
                              ? 1
                              : 0),
                      itemBuilder: (context, index) {
                        if (index >=
                            billingController.billingReports.length) {
                          return Container(
                            padding: const EdgeInsets.all(20),
                            child: const Center(
                              child: CircularProgressIndicator(),
                            ),
                          );
                        }
                        final billingData =
                            billingController.billingReports[index];
                        return BillingReportCard(
                          billingReport: billingData,
                          index: index,
                          permissionService: permissionService,
                          onDelete: () => _showDeleteConfirmation(billingData),
                          onEdit: () => Get.to(() => CreateBillingScreen(
                                mode: BillingScreenMode.edit,
                                billingData: BillingData(id: billingData.id),
                              )),
                        );
                      },
                    ),
                  );
                }),
              ),
            ),
          ],
        ),
        floatingActionButton: Visibility(
          visible: permissionService.hasPermission('billing', 'is_add'),
          child: FloatingActionButton(
            onPressed: () {
              Get.to(() => const CreateBillingScreen(
                mode: BillingScreenMode.create,
                showSearchOption: true,
              ));
            },
            child: const Icon(Icons.add),
          ),
        ),
      ),
    );
  }

  void _showDeleteConfirmation(BillingReportData billingData) {
    Get.dialog(
      AlertDialog(
        title: const Text('Delete Billing Report'),
        content: Text('Are you sure you want to delete the billing report for ${billingData.displayName}?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              // TODO: Implement delete functionality
              Get.snackbar(
                'Info',
                'Delete functionality not implemented yet',
                snackPosition: SnackPosition.BOTTOM,
              );
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}
